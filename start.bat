@echo off
chcp 65001 >nul
echo 正在启动网页自动化脚本工具...
echo.

REM 设置环境变量，强制使用系统浏览器
set PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
set PLAYWRIGHT_BROWSERS_PATH=0

REM 检查是否存在可执行文件
if not exist "gf-auto-archive.exe" (
    echo 错误: 找不到 gf-auto-archive.exe 文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 检查是否安装了 Chrome 或 Edge
where chrome.exe >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 检测到 Google Chrome
    goto :run_program
)

where msedge.exe >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 检测到 Microsoft Edge
    goto :run_program
)

REM 检查 Program Files 中的浏览器
if exist "%ProgramFiles%\Google\Chrome\Application\chrome.exe" (
    echo ✓ 检测到 Google Chrome
    goto :run_program
)

if exist "%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe" (
    echo ✓ 检测到 Google Chrome
    goto :run_program
)

if exist "%ProgramFiles(x86)%\Microsoft\Edge\Application\msedge.exe" (
    echo ✓ 检测到 Microsoft Edge
    goto :run_program
)

echo.
echo ❌ 错误: 未检测到支持的浏览器
echo.
echo 请安装以下浏览器之一：
echo   - Google Chrome: https://www.google.com/chrome/
echo   - Microsoft Edge: https://www.microsoft.com/edge/
echo.
pause
exit /b 1

:run_program
echo.
echo 正在启动程序...
echo.
gf-auto-archive.exe %*

if %errorlevel% neq 0 (
    echo.
    echo 程序执行出现错误，错误代码: %errorlevel%
    echo 请查看日志文件获取详细信息
    pause
)
